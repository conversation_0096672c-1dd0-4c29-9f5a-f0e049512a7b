package main

import (
	"fmt"
	"html/template" // Added for generating HTML
	"log"
	"math/rand"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// imageDir is the directory where images are stored.
const imageDir = "/srv/http/downloaded_booru_images_api/kasane_teto"
const staticPathPrefix = "/teto/static/" // URL prefix for serving static images
const randomRedirectPath = "/teto"      // URL path for the random image redirect
const tetoWebPageName = "teto-web.html" // Name of the HTML file for the random image viewer
const tetoWebPath = "/teto-web"         // URL path for the random image viewer webpage

// imageExtensions maps file extensions to their MIME types.
var imageExtensions = map[string]string{
	".jpg":  "image/jpeg",
	".jpeg": "image/jpeg",
	".png":  "image/png",
	".gif":  "image/gif",
}

// Template for the static index page (image list)
var indexTemplate = template.Must(template.New("index").Parse(`
<!DOCTYPE html>
<html>
<head>
    <title>Image Index</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f4f4f4; color: #333; }
        h1 { color: #5a5a5a; }
        ul { list-style-type: none; padding: 0; }
        li { margin: 5px 0; }
        a { text-decoration: none; color: #007bff; }
        a:hover { text-decoration: underline; }
        .container { max-width: 800px; margin: auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
    </style>
</head>
<body>
    <div class="container">
        <h1>Available Images</h1>
        <p>Click on an image name to view it. Total images: {{len .}}</p>
        <ul>
            {{range .}}
            <li><a href="{{.StaticPath}}{{.FileName}}">{{.FileName}}</a></li>
            {{end}}
        </ul>
    </div>
</body>
</html>
`))

// Struct to pass data to the index template
type IndexPageData struct {
	FileName   string
	StaticPath string
}

// init is called before main to seed the random number generator.
func init() {
	rand.Seed(time.Now().UnixNano())
}

// listAvailableImages scans the imageDir and returns a slice of valid image filenames.
func listAvailableImages() ([]string, error) {
	entries, err := os.ReadDir(imageDir)
	if err != nil {
		return nil, fmt.Errorf("reading directory %s: %w", imageDir, err)
	}

	var images []string
	for _, entry := range entries {
		if !entry.IsDir() {
			ext := strings.ToLower(filepath.Ext(entry.Name()))
			if _, ok := imageExtensions[ext]; ok {
				images = append(images, entry.Name())
			}
		}
	}
	return images, nil
}

// randomImageRedirectHandler handles requests to /teto.
// It selects a random image and redirects the client to its static URL.
func randomImageRedirectHandler(w http.ResponseWriter, r *http.Request) {
	images, err := listAvailableImages()
	if err != nil {
		log.Printf("Error listing available images: %v", err)
		http.Error(w, "Internal Server Error: Could not list images.", http.StatusInternalServerError)
		return
	}

	if len(images) == 0 {
		log.Printf("No images found in %s", imageDir)
		http.Error(w, "No images found.", http.StatusNotFound)
		return
	}

	randomIndex := rand.Intn(len(images))
	randomImageName := images[randomIndex]

	// Construct the redirect URL
	redirectURL := staticPathPrefix + randomImageName

	// Perform a temporary redirect (302)
	http.Redirect(w, r, redirectURL, http.StatusFound)
	log.Printf("Redirecting to: %s for a random image", redirectURL)
}

// staticIndexHandler serves an HTML page listing all available images.
func staticIndexHandler(w http.ResponseWriter, r *http.Request) {
	images, err := listAvailableImages()
	if err != nil {
		log.Printf("Error listing available images for index: %v", err)
		http.Error(w, "Internal Server Error: Could not list images.", http.StatusInternalServerError)
		return
	}

	if len(images) == 0 {
		log.Printf("No images found in %s to display in index.", imageDir)
		w.Header().Set("Content-Type", "text/html; charset=utf-8")
		fmt.Fprintln(w, "<h1>No images available</h1>")
		return
	}

	var pageData []IndexPageData
	for _, imgName := range images {
		pageData = append(pageData, IndexPageData{FileName: imgName, StaticPath: staticPathPrefix})
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	err = indexTemplate.Execute(w, pageData)
	if err != nil {
		log.Printf("Error executing HTML template: %v", err)
		http.Error(w, "Internal Server Error: Could not render index page.", http.StatusInternalServerError)
	}
	log.Printf("Served image index page for %s", staticPathPrefix)
}

// staticFileHandler handles requests for specific files under staticPathPrefix.
// It decides whether to serve an image or the index page.
func staticFileHandler(w http.ResponseWriter, r *http.Request) {
	// If the request path is exactly the staticPathPrefix, serve the index.
	if r.URL.Path == staticPathPrefix {
		staticIndexHandler(w, r)
		return
	}

	// Otherwise, try to serve a static file.
	imageName := strings.TrimPrefix(r.URL.Path, staticPathPrefix)

	if imageName == "" {
		http.NotFound(w, r)
		return
	}

	// Basic path traversal prevention
	if filepath.Base(imageName) != imageName {
		log.Printf("Attempted path traversal: %s", imageName)
		http.Error(w, "Bad Request: Invalid image name.", http.StatusBadRequest)
		return
	}

	imagePath := filepath.Join(imageDir, imageName)

	ext := strings.ToLower(filepath.Ext(imageName))
	contentType, ok := imageExtensions[ext]
	if !ok {
		log.Printf("Unsupported image type requested: %s (path: %s)", ext, imagePath)
		http.NotFound(w, r)
		return
	}

	img, err := os.Open(imagePath)
	if err != nil {
		if os.IsNotExist(err) {
			log.Printf("Image file not found: %s", imagePath)
			http.NotFound(w, r)
		} else {
			log.Printf("Error opening image file %s: %v", imagePath, err)
			http.Error(w, "Internal Server Error: Could not open image file.", http.StatusInternalServerError)
		}
		return
	}
	defer img.Close()

	fileInfo, err := img.Stat()
	if err != nil {
		log.Printf("Error getting file info for %s: %v", imagePath, err)
		http.Error(w, "Internal Server Error: Could not get image file info.", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", contentType)
	http.ServeContent(w, r, imageName, fileInfo.ModTime(), img)
	log.Printf("Served static image: %s", imageName)
}

// tetoWebHandler serves the teto-web.html page.
func tetoWebHandler(w http.ResponseWriter, r *http.Request) {
	// Serve the HTML file.
	// http.ServeFile will set the Content-Type based on the file extension.
	// It assumes tetoWebPageName is in the current working directory of the server.
	_, err := os.Stat(tetoWebPageName)
	if os.IsNotExist(err) {
		log.Printf("HTML file not found: %s", tetoWebPageName)
		http.NotFound(w, r)
		return
	} else if err != nil {
		log.Printf("Error checking HTML file %s: %v", tetoWebPageName, err)
		http.Error(w, "Internal Server Error: Could not check HTML file.", http.StatusInternalServerError)
		return
	}

	http.ServeFile(w, r, tetoWebPageName)
	log.Printf("Served HTML page: %s at %s", tetoWebPageName, tetoWebPath)
}

// corsMiddleware adds CORS headers to the response.
func corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Set CORS headers
		// Allow all origins, for more restrictive settings, replace "*" with your specific domain
		w.Header().Set("Access-Control-Allow-Origin", "*")
		// Allowed methods
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
		// Allowed headers
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")

		// Handle preflight requests (OPTIONS method)
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK) // Or http.StatusNoContent
			return
		}

		// Call the next handler in the chain
		next.ServeHTTP(w, r)
	})
}

func main() {
	// Check if the image directory exists
	if _, err := os.Stat(imageDir); os.IsNotExist(err) {
		log.Fatalf("Error: Image directory '%s' does not exist. Please create it and populate it with images.", imageDir)
	} else if err != nil {
		log.Fatalf("Error checking image directory '%s': %v", imageDir, err)
	}

	// Check if the teto-web.html file exists (optional, but good for startup feedback)
	if _, err := os.Stat(tetoWebPageName); os.IsNotExist(err) {
		log.Printf("Warning: HTML file '%s' not found in the current directory. The %s endpoint might not work.", tetoWebPageName, tetoWebPath)
	} else if err != nil {
		log.Printf("Warning: Error checking for HTML file '%s': %v", tetoWebPageName, err)
	}


	// Create a new ServeMux
	mux := http.NewServeMux()

	// Register handlers with the new mux
	mux.HandleFunc(randomRedirectPath, randomImageRedirectHandler)
	mux.HandleFunc(staticPathPrefix, staticFileHandler)
	mux.HandleFunc(tetoWebPath, tetoWebHandler) // Handler for the new HTML page

	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path != "/" {
			http.NotFound(w, r)
			return
		}
		// Updated welcome message to include the new page
		welcomeMessage := fmt.Sprintf(
			"Welcome! Visit %s to get a random image. Visit %s to see the image index. Visit %s to use the random image viewer web page.",
			randomRedirectPath, staticPathPrefix, tetoWebPath,
		)
		fmt.Fprint(w, welcomeMessage)
	})

	// Wrap the mux with the CORS middleware
	handlerWithCORS := corsMiddleware(mux)

	port := "5003"
	log.Printf("Starting server on port %s", port)
	log.Printf("Visit http://localhost:%s%s to get a random image (will redirect).", port, randomRedirectPath)
	log.Printf("Visit http://localhost:%s%s to see the image index.", port, staticPathPrefix)
	log.Printf("Static images are served from http://localhost:%s%s<image_name>", port, staticPathPrefix)
	log.Printf("Visit http://localhost:%s%s to use the random image viewer page.", port, tetoWebPath) // Log for the new page

	// Start the server with the CORS-enabled handler
	if err := http.ListenAndServe(":"+port, handlerWithCORS); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
